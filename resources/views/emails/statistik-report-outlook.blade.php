<!DOCTYPE html>
<html lang="de" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="x-apple-disable-message-reformatting">
    <title>Statistik-Report</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        
        img {
            -ms-interpolation-mode: bicubic;
        }
        
        /* Outlook-spezifische Styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333333;
            background-color: #f5f5f5;
        }
        
        .email-wrapper {
            width: 100%;
            background-color: #f5f5f5;
        }
        
        .email-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #ffffff;
        }
        
        .header-table {
            width: 100%;
            background-color: #003062;
            color: #ffffff;
        }
        
        .header-cell {
            padding: 30px;
            text-align: center;
        }
        
        .header-title {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
            color: #ffffff;
        }
        
        .header-subtitle {
            margin: 10px 0 0 0;
            font-size: 16px;
            color: #ffffff;
        }
        
        .stats-wrapper {
            padding: 20px;
        }
        
        .stats-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .stat-cell {
            width: 20%;
            padding: 10px;
            vertical-align: top;
        }
        
        .stat-card {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 20px;
            text-align: center;
        }
        
        .stat-title {
            margin: 0 0 10px 0;
            color: #003062;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #005aaa;
            margin: 0;
        }
        
        .section-wrapper {
            padding: 0 20px 20px 20px;
        }
        
        .section {
            background-color: #ffffff;
            border: 1px solid #e9ecef;
            padding: 25px;
            margin-bottom: 20px;
        }
        
        .section-title {
            color: #003062;
            margin: 0 0 20px 0;
            font-size: 20px;
            font-weight: bold;
            border-bottom: 2px solid #005aaa;
            padding-bottom: 10px;
        }
        
        .list-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .list-row {
            border-bottom: 1px solid #f1f3f4;
        }
        
        .list-name {
            padding: 12px 0;
            text-align: left;
            vertical-align: middle;
        }
        
        .list-value {
            padding: 12px 0;
            text-align: right;
            vertical-align: middle;
            font-weight: bold;
            color: #005aaa;
        }
        
        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 8px;
            border-radius: 3px;
        }
        
        .badge-success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .badge-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .badge-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .highlight {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            background-color: #f8f9fa;
            color: #6c757d;
            font-size: 14px;
        }
        
        /* Mobile responsive */
        @media only screen and (max-width: 600px) {
            .stat-cell {
                width: 50% !important;
            }
        }
    </style>
</head>
<body>
    <table class="email-wrapper" cellpadding="0" cellspacing="0" border="0">
        <tr>
            <td align="center">
                <table class="email-container" cellpadding="0" cellspacing="0" border="0">
                    <!-- Header -->
                    <tr>
                        <td>
                            <table class="header-table" cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                    <td class="header-cell">
                                        <h1 class="header-title">📊 Rotekarten Statistik-Report</h1>
                                        <p class="header-subtitle">Zeitraum: {{ $zeitraum }}</p>
                                        <p class="header-subtitle">Erstellt am: {{ now()->format('d.m.Y H:i') }} Uhr</p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Statistics Cards -->
                    <tr>
                        <td class="stats-wrapper">
                            <table class="stats-table" cellpadding="0" cellspacing="0" border="0">
                                <tr>
                                    <td class="stat-cell">
                                        <div class="stat-card">
                                            <h3 class="stat-title">Gesamt</h3>
                                            <div class="stat-number">{{ $statistiken['gesamt']['total'] }}</div>
                                        </div>
                                    </td>
                                    <td class="stat-cell">
                                        <div class="stat-card">
                                            <h3 class="stat-title">Heute</h3>
                                            <div class="stat-number">{{ $statistiken['gesamt']['heute'] }}</div>
                                        </div>
                                    </td>
                                    <td class="stat-cell">
                                        <div class="stat-card">
                                            <h3 class="stat-title">Diese Woche</h3>
                                            <div class="stat-number">{{ $statistiken['gesamt']['diese_woche'] }}</div>
                                        </div>
                                    </td>
                                    <td class="stat-cell">
                                        <div class="stat-card">
                                            <h3 class="stat-title">Dieser Monat</h3>
                                            <div class="stat-number">{{ $statistiken['gesamt']['dieser_monat'] }}</div>
                                        </div>
                                    </td>
                                    <td class="stat-cell">
                                        <div class="stat-card">
                                            <h3 class="stat-title">Ø pro Tag</h3>
                                            <div class="stat-number">{{ $statistiken['gesamt']['durchschnitt_pro_tag'] }}</div>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Content Sections -->
                    <tr>
                        <td class="section-wrapper">
                            <!-- Status-Verteilung -->
                            <div class="section">
                                <h2 class="section-title">📋 Status-Verteilung</h2>
                                <table class="list-table" cellpadding="0" cellspacing="0" border="0">
                                    @foreach($statistiken['status'] as $status)
                                    <tr class="list-row">
                                        <td class="list-name">
                                            {{ $status['status'] }}
                                            @if($status['status'] === 'Abgeschlossen')
                                                <span class="badge badge-success">✓</span>
                                            @elseif($status['status'] === 'In Bearbeitung')
                                                <span class="badge badge-warning">⏳</span>
                                            @else
                                                <span class="badge badge-danger">⚠️</span>
                                            @endif
                                        </td>
                                        <td class="list-value">{{ $status['anzahl'] }}</td>
                                    </tr>
                                    @endforeach
                                </table>
                            </div>
                            
                            <!-- Abteilungsanalyse -->
                            <div class="section">
                                <h2 class="section-title">🏭 Abteilungsanalyse</h2>
                                <table class="list-table" cellpadding="0" cellspacing="0" border="0">
                                    @foreach($statistiken['abteilungen'] as $abteilung)
                                    <tr class="list-row">
                                        <td class="list-name">{{ $abteilung['abteilung'] }}</td>
                                        <td class="list-value">{{ $abteilung['anzahl'] }}</td>
                                    </tr>
                                    @endforeach
                                </table>
                            </div>

                            <!-- Qualitätsmetriken -->
                            <div class="section">
                                <h2 class="section-title">📈 Qualitätsmetriken</h2>
                                <div class="highlight">
                                    <strong>Abschlussrate:</strong> {{ $statistiken['qualitaet']['abschluss_rate'] }}%
                                </div>
                                <table class="list-table" cellpadding="0" cellspacing="0" border="0">
                                    <tr class="list-row">
                                        <td class="list-name">Abgeschlossen</td>
                                        <td class="list-value">{{ $statistiken['qualitaet']['abgeschlossen'] }}</td>
                                    </tr>
                                    <tr class="list-row">
                                        <td class="list-name">In Bearbeitung</td>
                                        <td class="list-value">{{ $statistiken['qualitaet']['in_bearbeitung'] }}</td>
                                    </tr>
                                    <tr class="list-row">
                                        <td class="list-name">Offen</td>
                                        <td class="list-value">{{ $statistiken['qualitaet']['offen'] }}</td>
                                    </tr>
                                    <tr class="list-row">
                                        <td class="list-name">Ø Bearbeitungszeit</td>
                                        <td class="list-value">{{ $statistiken['qualitaet']['avg_bearbeitungszeit_stunden'] }}h</td>
                                    </tr>
                                </table>
                            </div>

                            <!-- Problembereiche -->
                            @if(count($statistiken['problembereiche']) > 0)
                            <div class="section">
                                <h2 class="section-title">🚨 Problembereiche</h2>
                                <table class="list-table" cellpadding="0" cellspacing="0" border="0">
                                    @foreach($statistiken['problembereiche'] as $problem)
                                    <tr class="list-row">
                                        <td class="list-name">{{ $problem['bereich'] }}</td>
                                        <td class="list-value">{{ $problem['anzahl_probleme'] }}</td>
                                    </tr>
                                    @endforeach
                                </table>
                            </div>
                            @endif

                            <!-- Erweiterte Analysen -->
                            @if(isset($statistiken['analyseabweichungen']) && count($statistiken['analyseabweichungen']) > 0)
                            <div class="section">
                                <h2 class="section-title">🧪 Top Analyseabweichungen</h2>
                                <table class="list-table" cellpadding="0" cellspacing="0" border="0">
                                    @foreach($statistiken['analyseabweichungen']->take(5) as $abweichung)
                                    <tr class="list-row">
                                        <td class="list-name">
                                            {{ $abweichung['element'] }}
                                            <span class="badge badge-warning">{{ $abweichung['durchschnitt_abweichung'] }}%</span>
                                        </td>
                                        <td class="list-value">{{ $abweichung['anzahl_abweichungen'] }}</td>
                                    </tr>
                                    @endforeach
                                </table>
                            </div>
                            @endif

                            @if(isset($statistiken['eisenmarken']) && count($statistiken['eisenmarken']) > 0)
                            <div class="section">
                                <h2 class="section-title">⚙️ Top Eisenmarken</h2>
                                <table class="list-table" cellpadding="0" cellspacing="0" border="0">
                                    @foreach($statistiken['eisenmarken']->take(5) as $eisenmarke)
                                    <tr class="list-row">
                                        <td class="list-name">{{ $eisenmarke['eisenmarke'] }}</td>
                                        <td class="list-value">{{ $eisenmarke['anzahl_rotekarten'] }}</td>
                                    </tr>
                                    @endforeach
                                </table>
                            </div>
                            @endif

                            @if(isset($statistiken['mitarbeiter']) && count($statistiken['mitarbeiter']) > 0)
                            <div class="section">
                                <h2 class="section-title">👥 Top Mitarbeiter</h2>
                                <table class="list-table" cellpadding="0" cellspacing="0" border="0">
                                    @foreach($statistiken['mitarbeiter']->take(5) as $mitarbeiter)
                                    <tr class="list-row">
                                        <td class="list-name">{{ $mitarbeiter['name'] }}</td>
                                        <td class="list-value">{{ $mitarbeiter['anzahl_rotekarten'] }}</td>
                                    </tr>
                                    @endforeach
                                </table>
                            </div>
                            @endif
                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        <td>
                            <div class="footer">
                                <p>Dieser Report wurde automatisch generiert von der Rotekarten-Verwaltung.</p>
                                <p>Bei Fragen wenden Sie sich an das Qualitätsmanagement.</p>
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
