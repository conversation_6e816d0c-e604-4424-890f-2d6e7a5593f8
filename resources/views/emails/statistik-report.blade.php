<!DOCTYPE html>
<html lang="de" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="x-apple-disable-message-reformatting">
    <title>Statistik-Report</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Outlook-kompatible Styles */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .email-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #ffffff;
        }

        .header {
            background-color: #003062;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }

        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
        }

        /* Tabellen-basiertes Layout für Outlook-Kompatibilität */
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        .stats-table td {
            width: 20%;
            padding: 10px;
            vertical-align: top;
            mso-line-height-rule: exactly;
        }

        .stat-card {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 20px;
            text-align: center;
            margin: 5px;
        }

        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #003062;
            font-size: 14px;
            text-transform: uppercase;
            font-weight: bold;
        }

        .stat-card .number {
            font-size: 32px;
            font-weight: bold;
            color: #005aaa;
            margin: 0;
        }

        .section {
            background-color: white;
            border: 1px solid #e9ecef;
            padding: 25px;
            margin: 20px;
        }

        .section h2 {
            color: #003062;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 20px;
            border-bottom: 2px solid #005aaa;
            padding-bottom: 10px;
        }

        .list-table {
            width: 100%;
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        .list-table tr {
            border-bottom: 1px solid #f1f3f4;
        }

        .list-table tr:last-child {
            border-bottom: none;
        }

        .list-table td {
            padding: 12px 0;
            vertical-align: middle;
            mso-line-height-rule: exactly;
        }

        .list-table .name {
            text-align: left;
        }

        .list-table .value {
            text-align: right;
            font-weight: bold;
            color: #005aaa;
        }

        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 8px;
            border-radius: 3px;
        }

        .badge-success {
            background-color: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background-color: #fff3cd;
            color: #856404;
        }

        .badge-danger {
            background-color: #f8d7da;
            color: #721c24;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f9fa;
            color: #6c757d;
            font-size: 14px;
        }

        .highlight {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
        }

        /* Outlook-spezifische Fixes */
        .mso-hide {
            mso-hide: all;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>📊 Rotekarten Statistik-Report</h1>
            <p>Zeitraum: {{ $zeitraum }}</p>
            <p>Erstellt am: {{ now()->format('d.m.Y H:i') }} Uhr</p>
        </div>

        <!-- Gesamtstatistiken als Tabelle für Outlook-Kompatibilität -->
        <table class="stats-table" cellpadding="0" cellspacing="0">
            <tr>
                <td>
                    <div class="stat-card">
                        <h3>Gesamt</h3>
                        <div class="number">{{ $statistiken['gesamt']['total'] }}</div>
                    </div>
                </td>
                <td>
                    <div class="stat-card">
                        <h3>Heute</h3>
                        <div class="number">{{ $statistiken['gesamt']['heute'] }}</div>
                    </div>
                </td>
                <td>
                    <div class="stat-card">
                        <h3>Diese Woche</h3>
                        <div class="number">{{ $statistiken['gesamt']['diese_woche'] }}</div>
                    </div>
                </td>
                <td>
                    <div class="stat-card">
                        <h3>Dieser Monat</h3>
                        <div class="number">{{ $statistiken['gesamt']['dieser_monat'] }}</div>
                    </div>
                </td>
                <td>
                    <div class="stat-card">
                        <h3>Ø pro Tag</h3>
                        <div class="number">{{ $statistiken['gesamt']['durchschnitt_pro_tag'] }}</div>
                    </div>
                </td>
            </tr>
        </table>

        <!-- Status-Verteilung -->
        <div class="section">
            <h2>📋 Status-Verteilung</h2>
            <table class="list-table" cellpadding="0" cellspacing="0">
                @foreach($statistiken['status'] as $status)
                <tr>
                    <td class="name">
                        {{ $status['status'] }}
                        @if($status['status'] === 'Abgeschlossen')
                            <span class="badge badge-success">✓</span>
                        @elseif($status['status'] === 'In Bearbeitung')
                            <span class="badge badge-warning">⏳</span>
                        @else
                            <span class="badge badge-danger">⚠️</span>
                        @endif
                    </td>
                    <td class="value">{{ $status['anzahl'] }}</td>
                </tr>
                @endforeach
            </table>
        </div>

        <!-- Abteilungsanalyse -->
        <div class="section">
            <h2>🏭 Abteilungsanalyse</h2>
            <table class="list-table" cellpadding="0" cellspacing="0">
                @foreach($statistiken['abteilungen'] as $abteilung)
                <tr>
                    <td class="name">{{ $abteilung['abteilung'] }}</td>
                    <td class="value">{{ $abteilung['anzahl'] }}</td>
                </tr>
                @endforeach
            </table>
        </div>

        <!-- Qualitätsmetriken -->
        <div class="section">
            <h2>📈 Qualitätsmetriken</h2>
            <div class="highlight">
                <strong>Abschlussrate:</strong> {{ $statistiken['qualitaet']['abschluss_rate'] }}%
            </div>
            <table class="list-table" cellpadding="0" cellspacing="0">
                <tr>
                    <td class="name">Abgeschlossen</td>
                    <td class="value">{{ $statistiken['qualitaet']['abgeschlossen'] }}</td>
                </tr>
                <tr>
                    <td class="name">In Bearbeitung</td>
                    <td class="value">{{ $statistiken['qualitaet']['in_bearbeitung'] }}</td>
                </tr>
                <tr>
                    <td class="name">Offen</td>
                    <td class="value">{{ $statistiken['qualitaet']['offen'] }}</td>
                </tr>
                <tr>
                    <td class="name">Ø Bearbeitungszeit</td>
                    <td class="value">{{ $statistiken['qualitaet']['avg_bearbeitungszeit_stunden'] }}h</td>
                </tr>
            </table>
        </div>

        <!-- Problembereiche -->
        @if(count($statistiken['problembereiche']) > 0)
        <div class="section">
            <h2>🚨 Problembereiche</h2>
            <table class="list-table" cellpadding="0" cellspacing="0">
                @foreach($statistiken['problembereiche'] as $problem)
                <tr>
                    <td class="name">{{ $problem['bereich'] }}</td>
                    <td class="value">{{ $problem['anzahl_probleme'] }}</td>
                </tr>
                @endforeach
            </table>
        </div>
        @endif

        <!-- Erweiterte Analysen -->
        @if(isset($statistiken['analyseabweichungen']) && count($statistiken['analyseabweichungen']) > 0)
        <div class="section">
            <h2>🧪 Top Analyseabweichungen</h2>
            <table class="list-table" cellpadding="0" cellspacing="0">
                @foreach($statistiken['analyseabweichungen']->take(5) as $abweichung)
                <tr>
                    <td class="name">
                        {{ $abweichung['element'] }}
                        <span class="badge badge-warning">{{ $abweichung['durchschnitt_abweichung'] }}%</span>
                    </td>
                    <td class="value">{{ $abweichung['anzahl_abweichungen'] }}</td>
                </tr>
                @endforeach
            </table>
        </div>
        @endif

        @if(isset($statistiken['eisenmarken']) && count($statistiken['eisenmarken']) > 0)
        <div class="section">
            <h2>⚙️ Top Eisenmarken</h2>
            <table class="list-table" cellpadding="0" cellspacing="0">
                @foreach($statistiken['eisenmarken']->take(5) as $eisenmarke)
                <tr>
                    <td class="name">{{ $eisenmarke['eisenmarke'] }}</td>
                    <td class="value">{{ $eisenmarke['anzahl_rotekarten'] }}</td>
                </tr>
                @endforeach
            </table>
        </div>
        @endif

        @if(isset($statistiken['mitarbeiter']) && count($statistiken['mitarbeiter']) > 0)
        <div class="section">
            <h2>👥 Top Mitarbeiter</h2>
            <table class="list-table" cellpadding="0" cellspacing="0">
                @foreach($statistiken['mitarbeiter']->take(5) as $mitarbeiter)
                <tr>
                    <td class="name">{{ $mitarbeiter['name'] }}</td>
                    <td class="value">{{ $mitarbeiter['anzahl_rotekarten'] }}</td>
                </tr>
                @endforeach
            </table>
        </div>
        @endif

        <div class="footer">
            <p>Dieser Report wurde automatisch generiert von der Rotekarten-Verwaltung.</p>
            <p>Bei Fragen wenden Sie sich an das Qualitätsmanagement.</p>
        </div>
    </div>
</body>
</html>
