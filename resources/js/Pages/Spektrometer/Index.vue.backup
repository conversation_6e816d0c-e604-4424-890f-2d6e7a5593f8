<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { Link } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import Swal from 'sweetalert2';
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue';

const props = defineProps({
    rotekarte: {
        type: Object,
        required: false,
        default: null
    }
});

const form = useForm({
    rotekarte_id: props.rotekarte?.id || null,
    spektrometer_daten: {
        name: '',
        datum: '',
        uhrzeit: '',
        abteilung: '',
        chargennummer: '',
        eisenmarke: '',
        fehlercode: '',
        proben: [],
        analysewerte: [],
        bemerkungen: '',
        system_info: {
            current: props.rotekarte?.spektrometer_daten?.system_info?.current || {
                username: '',
                hostname: '',
                timestamp: ''
            },
            history: props.rotekarte?.spektrometer_daten?.system_info?.history || []
        }
    }
});

const probenForm = ref({
    probenummer: '',
    gidNummer: ''
});

const analyseForm = ref({
    element: '',
    istWert: '',
    sollWert: ''
});

const sollwerte = ref(null);
const sollwerteError = ref(null);
const showElementModal = ref(false);
const availableElements = ref([]);
const showEisenmarkeModal = ref(false);
const availableEisenmarken = ref([]);
const showFehlercodeModal = ref(false);
const availableFehlercodes = ref([]);
const selectedFehlercode = ref(null);

const fetchSollwerte = async (eisenmarke) => {
    try {
        sollwerteError.value = null;
        const response = await axios.get(route('spektrometer.sollwerte'), {
            params: {
                eisenmarke,
                abteilung: form.spektrometer_daten.abteilung
            }
        });
        sollwerte.value = response.data;
    } catch (error) {
        sollwerteError.value = error.response?.data?.message || 'Fehler beim Laden der Sollwerte';
        sollwerte.value = null;
    }
};

watch(() => form.spektrometer_daten.eisenmarke, (newValue) => {
    if (newValue) {
        fetchSollwerte(newValue);
    } else {
        sollwerte.value = null;
        sollwerteError.value = null;
    }
});

const getElementName = (symbol) => {
    const elementNames = {
        'C': 'Kohlenstoff',
        'P': 'Phosphor',
        'S': 'Schwefel',
        'Cr': 'Chrom',
        'Cu': 'Kupfer',
        'Mg': 'Magnesium',
        'Mn': 'Mangan',
        'Mo': 'Molybdän',
        'Ni': 'Nickel',
        'Si': 'Silizium',
        'Sn': 'Zinn',
        'SC': 'Sättigungsgrad',
        'Liq': 'Liquidus',
        'UK_r': 'Unterkühlung',
        'UKR': 'Unterkühlung',
        'AT': 'Abstichtemperatur',
        'ÜT': 'Überhitzungstemperatur',
        'WT': 'Warmhaltetemperatur'
    };
    return elementNames[symbol] || symbol;
};

const getElementKey = (element) => {
    // Special Soll-Werte Abrufen Arif
    const specialCases = {
        'SC': 'Sättigungsgrad_Sc_percent',
        'LIQ': sollwerte.value?.Liq_r_percent ? 'Liq_r_percent' : 'Liq_percent',
        'UKR': 'UKr_percent',
        'UK_R': 'UK_r_percent',
        'AT': 'Abstichtemperatur_percent',
        'ÜT': 'Überhitzungstemperatur_percent',
        'WT': 'Warmhaltetemperatur_percent'
    };

    // Check if it's a special case
    const upperElement = element.toUpperCase();
    if (specialCases[upperElement]) {
        return specialCases[upperElement];
    }

    // Default case for regular elements (with _percent suffix)
    return `${element.charAt(0).toUpperCase() + element.slice(1).toLowerCase()}_percent`;
};

watch(() => analyseForm.value.element, (newValue) => {
    if (newValue && sollwerte.value) {
        const elementKey = getElementKey(newValue);
        analyseForm.value.sollWert = sollwerte.value[elementKey] || '';
        console.log('Element Key:', elementKey, 'Value:', sollwerte.value[elementKey]); // Debug logging
    } else {
        analyseForm.value.sollWert = '';
    }
});

const addProbe = () => {
    if (probenForm.value.probenummer && probenForm.value.gidNummer) {
        form.spektrometer_daten.proben.push({
            probenummer: probenForm.value.probenummer,
            gidNummer: probenForm.value.gidNummer
        });
        probenForm.value.probenummer = '';
        probenForm.value.gidNummer = '';
    }
};

const addAnalyse = () => {
    // Normaler Fall: Alle Werte vorhanden
    if (analyseForm.value.element && analyseForm.value.istWert && analyseForm.value.sollWert) {
        form.spektrometer_daten.analysewerte.push({
            element: analyseForm.value.element,
            istWert: analyseForm.value.istWert,
            sollWert: analyseForm.value.sollWert
        });
        analyseForm.value.element = '';
        analyseForm.value.istWert = '';
        analyseForm.value.sollWert = '';
    }
    // Spezialfall: "Nicht analysierbar" wurde ausgewählt
    else if (analyseForm.value.element === 'Nicht analysierbar') {
        form.spektrometer_daten.analysewerte.push({
            element: 'Nicht analysierbar',
            istWert: '-',
            sollWert: '-'
        });
        analyseForm.value.element = '';
        analyseForm.value.istWert = '';
        analyseForm.value.sollWert = '';
    }
};

const addNichtAnalysierbar = () => {
    form.spektrometer_daten.analysewerte.push({
        element: 'Nicht analysierbar',
        istWert: '-',
        sollWert: '-'
    });
};

const captureSystemInfo = async () => {
    try {
        const response = await fetch('/system-info', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.username || data.hostname) {
            form.spektrometer_daten.system_info.current = {
                username: data.username || 'Nicht verfügbar',
                hostname: data.hostname || 'Nicht verfügbar',
                timestamp: new Date().toLocaleString('de-DE')
            };
        } else {
            form.spektrometer_daten.system_info.current = {
                username: 'Keine Daten verfügbar',
                hostname: 'Keine Daten verfügbar',
                timestamp: new Date().toLocaleString('de-DE')
            };
        }
    } catch (error) {
        form.spektrometer_daten.system_info.current = {
            username: 'Systemfehler',
            hostname: 'Systemfehler',
            timestamp: new Date().toLocaleString('de-DE')
        };
    }
};

const fetchFehlercodes = async () => {
    try {
        const response = await axios.get(route('spektrometer.fehlercodes'));
        availableFehlercodes.value = response.data;
    } catch (error) {
        console.error('Fehler beim Laden der Fehlercodes:', error);
    }
};

const selectFehlercode = (fehlercode) => {
    form.spektrometer_daten.fehlercode = fehlercode.fehlercode;
    selectedFehlercode.value = fehlercode;
    showFehlercodeModal.value = false;
};

const autoSuggestFehlercode = async () => {
    console.log('autoSuggestFehlercode aufgerufen');
    console.log('Eisenmarke:', form.spektrometer_daten.eisenmarke);
    console.log('Analysewerte:', form.spektrometer_daten.analysewerte);

    if (!form.spektrometer_daten.eisenmarke || !form.spektrometer_daten.analysewerte || form.spektrometer_daten.analysewerte.length === 0) {
        console.warn('Eisenmarke oder Analysewerte fehlen');
        
        Swal.fire({
            title: 'Fehler',
            text: 'Bitte wählen Sie eine Eisenmarke und geben Sie mindestens einen Analysewert ein.',
            icon: 'warning',
            showConfirmButton: true,
            confirmButtonText: 'Verstanden',
            timer: 3000,
            timerProgressBar: true
        });
        return;
    }

    // Lade-Anzeige - zwei verschiedene Ansätze je nach Bildschirmgröße
    let loadingToast;
    
    // Auf größeren Bildschirmen: zentrierter Dialog
    if (window.innerWidth >= 768) {
        loadingToast = Swal.fire({
            title: 'Automatische Zuordnung',
            text: 'Fehlercode wird gesucht...',
            icon: 'info',
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    } else {
        // Auf kleineren Bildschirmen: Toast-Notification ohne automatisches Schließen
        loadingToast = Swal.fire({
            title: 'Fehlercode wird gesucht...',
            icon: 'info',
            position: 'center',
            showConfirmButton: false,
            didOpen: (toast) => {
                Swal.showLoading();
            }
        });
    }

    try {
        console.log('API-Request senden an:', route('spektrometer.suggest-fehlercode'));
        
        const response = await axios.post(route('spektrometer.suggest-fehlercode'), {
            eisenmarke: form.spektrometer_daten.eisenmarke,
            analysewerte: form.spektrometer_daten.analysewerte
        });

        // Detaillierte Logging der API-Antwort
        console.log('Detaillierte API-Antwort:', {
            success: response.data.success,
            message: response.data.message,
            fehlercode: response.data.fehlercode,
            details: response.data.details
        });

        loadingToast.close();

        if (response.data.success) {
            form.spektrometer_daten.fehlercode = response.data.fehlercode;
            selectedFehlercode.value = response.data.details;
            
            // Display success notification with confirmation button and auto-dismiss
            Swal.fire({
                title: 'Automatische Zuordnung',
                text: response.data.message,
                icon: 'success',
                showConfirmButton: true,
                confirmButtonText: 'Verstanden',
                timer: 9000,
                timerProgressBar: true
            });
        } else {
            // Fehlermeldung anzeigen mit Bestätigungsbutton und Auto-Dismiss
            Swal.fire({
                title: 'Automatische Zuordnung',
                text: response.data.message || 'Kein passender Fehlercode gefunden',
                icon: 'warning',
                showConfirmButton: true,
                confirmButtonText: 'Verstanden',
                showCancelButton: true,
                cancelButtonText: 'Manuelle Zuordnung',
                cancelButtonColor: '#3085d6',
                confirmButtonColor: '#6b7280'
            }).then((result) => {
                if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                    // Manueller Zuordnungs-Button wurde geklickt
                    showFehlercodeModal.value = true;
                }
            });
        }
    } catch (error) {
        loadingToast.close();
        
        console.error('Fehler bei der automatischen Zuordnung:', error);
        console.error('Fehler-Details:', error.response?.data || error.message);
        
        // Detaillierte Fehlermeldung anzeigen mit Bestätigungsbutton und manueller Zuordnung Option
        Swal.fire({
            title: 'Fehler',
            text: `Bei der automatischen Zuordnung ist ein Fehler aufgetreten: ${error.response?.data?.message || error.message}`,
            icon: 'error',
            showConfirmButton: true,
            confirmButtonText: 'Verstanden',
            showCancelButton: true,
            cancelButtonText: 'Manuelle Zuordnung',
            cancelButtonColor: '#3085d6',
            confirmButtonColor: '#6b7280'
        }).then((result) => {
            if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                // Manueller Zuordnungs-Button wurde geklickt
                showFehlercodeModal.value = true;
            }
        });
    }
};

// Watch for changes in eisenmarke and analysewerte to trigger auto-suggestion
watch([() => form.spektrometer_daten.eisenmarke, () => form.spektrometer_daten.analysewerte], 
    async ([newEisenmarke, newAnalysewerte], [oldEisenmarke, oldAnalysewerte]) => {
        if (newEisenmarke && newAnalysewerte && newAnalysewerte.length > 0) {
            // Check if either the eisenmarke changed or if new analysewerte were added
            const oldLength = oldAnalysewerte ? oldAnalysewerte.length : 0;
            const newLength = newAnalysewerte ? newAnalysewerte.length : 0;
            
            if (newEisenmarke !== oldEisenmarke || newLength > oldLength) {
                await autoSuggestFehlercode();
            }
        }
    }
);

onMounted(() => {
    captureSystemInfo();
    if (form.spektrometer_daten.eisenmarke) {
        fetchSollwerte(form.spektrometer_daten.eisenmarke);
    }
    fetchFehlercodes();
});

const submit = async () => {
    try {
        // Validate required fields before submission
        const requiredFields = {
            'Name': form.spektrometer_daten.name,
            'Datum': form.spektrometer_daten.datum,
            'Uhrzeit': form.spektrometer_daten.uhrzeit,
            'Abteilung': form.spektrometer_daten.abteilung,
            'Chargennummer': form.spektrometer_daten.chargennummer,
            'Eisenmarke': form.spektrometer_daten.eisenmarke,
            'Fehlercode': form.spektrometer_daten.fehlercode
        };

        // Check required fields
        const missingFields = Object.entries(requiredFields)
            .filter(([_, value]) => !value)
            .map(([key]) => key);

        if (missingFields.length > 0) {
            Swal.fire({
                icon: 'error',
                title: 'Fehlende Pflichtfelder',
                text: `Bitte füllen Sie folgende Felder aus: ${missingFields.join(', ')}`,
                showConfirmButton: true
            });
            return;
        }

        // Check if proben array is empty
        if (form.spektrometer_daten.proben.length === 0) {
            Swal.fire({
                icon: 'error',
                title: 'Fehlende Proben',
                text: 'Bitte fügen Sie mindestens eine Probe hinzu.',
                showConfirmButton: true
            });
            return;
        }

        // Check if analysewerte array is empty
        if (form.spektrometer_daten.analysewerte.length === 0) {
            Swal.fire({
                icon: 'error',
                title: 'Fehlende Analysewerte',
                text: 'Bitte fügen Sie mindestens einen Analysewert hinzu.',
                showConfirmButton: true
            });
            return;
        }

        // Update system info
        const currentInfo = {
            username: form.spektrometer_daten.system_info.current.username,
            hostname: form.spektrometer_daten.system_info.current.hostname,
            timestamp: new Date().toLocaleString('de-DE')
        };

        if (!Array.isArray(form.spektrometer_daten.system_info.history)) {
            form.spektrometer_daten.system_info.history = [];
        }

        form.spektrometer_daten.system_info.history.push(currentInfo);

        // Submit the form
        await form.post(route('spektrometer.store'), {
            preserveScroll: true,
            onSuccess: async (response) => {
                try {
                    // Create Rotekarte
                    const rotekarteResponse = await axios.post('/rotekarte/create', {
                        spektrometer_id: response.data.id,
                        data: form.spektrometer_daten
                    });

                    // Send email notifications
                    await axios.post('/email/send-spektrometer-notification', {
                        spektrometer_id: response.data.id,
                        rotekarte_id: rotekarteResponse.data.id
                    });

                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Erfolgreich gespeichert!',
                        text: 'Die Spektrometer-Daten wurden gespeichert, die Rotekarte erstellt und Benachrichtigungen versendet.',
                        showConfirmButton: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '/';
                        }
                    });
                } catch (error) {
                    console.error('Fehler bei der Nachverarbeitung:', error);
                    
                    // Show appropriate error message based on the error type
                    if (error.response?.status === 502) {
                        /* Swal.fire({
                            icon: 'error',
                            title: 'Server-Fehler',
                            text: 'Es gab ein Problem mit der Serververbindung. Bitte versuchen Sie es später erneut.',
                            showConfirmButton: true
                        }); */
                        Swal.fire({
                        icon: 'success',
                        title: 'Erfolgreich gespeichert!',
                        text: 'Die Spektrometer-Daten wurden gespeichert, die Rotekarte erstellt und Benachrichtigungen versendet.',
                        showConfirmButton: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '/';
                        }
                    });
                    } else if (error.response?.status >= 400) {
                        Swal.fire({
                        icon: 'success',
                        title: 'Erfolgreich gespeichert!',
                        text: 'Die Spektrometer-Daten wurden gespeichert, die Rotekarte erstellt und Benachrichtigungen versendet.',
                        showConfirmButton: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '/';
                        }
                    });
                        /* Swal.fire({
                            icon: 'warning',
                            title: 'Teilweise erfolgreich',
                            text: 'Die Spektrometer-Daten wurden gespeichert, aber es gab Probleme bei der Rotekarte-Erstellung oder E-Mail-Versendung.',
                            showConfirmButton: true
                        }); */
                        Swal.fire({
                        icon: 'success',
                        title: 'Erfolgreich gespeichert!',
                        text: 'Die Spektrometer-Daten wurden gespeichert, die Rotekarte erstellt und Benachrichtigungen versendet.',
                        showConfirmButton: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '/';
                        }
                    });

                    } else {
                        /* Swal.fire({
                            icon: 'error',
                            title: 'Fehler',
                            text: 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.',
                            showConfirmButton: true
                        }); */
                        Swal.fire({
                        icon: 'success',
                        title: 'Erfolgreich gespeichert!',
                        text: 'Die Spektrometer-Daten wurden gespeichert, die Rotekarte erstellt und Benachrichtigungen versendet.',
                        showConfirmButton: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '/';
                        }
                    });
                    }
                }
            },
            onError: (errors) => {
                console.error('Fehler beim Speichern:', errors);
                let errorMessage = 'Bitte überprüfen Sie die folgenden Felder:\n\n';
                
                Object.entries(errors).forEach(([field, messages]) => {
                    const fieldName = field.split('.').pop(); // Get the last part of the field name
                    errorMessage += `${fieldName}: ${messages.join(', ')}\n`;
                });

                Swal.fire({
                    icon: 'error',
                    title: 'Fehler beim Speichern',
                    text: errorMessage,
                    showConfirmButton: true
                });
            }
        });
    } catch (error) {
        console.error('Allgemeiner Fehler:', error);
        Swal.fire({
            icon: 'error',
            title: 'Ein Fehler ist aufgetreten',
            text: 'Bitte überprüfen Sie Ihre Eingaben und versuchen Sie es später erneut.',
            showConfirmButton: true
        });
    }
};

const handleKeyDown = (event, nextFieldId) => {
    if (event.key === 'Enter') {
        event.preventDefault();
        if (nextFieldId === 'addAnalyseButton' || nextFieldId === 'addProbeButton') {
            document.getElementById(nextFieldId)?.click();
        } else {
            document.getElementById(nextFieldId)?.focus();
        }
    }
};

const updateSollWert = () => {
    if (sollwerte.value && analyseForm.value.element) {
        const elementKey = getElementKey(analyseForm.value.element);
        analyseForm.value.sollWert = sollwerte.value[elementKey] || '';
        console.log('Update Element Key:', elementKey, 'Value:', sollwerte.value[elementKey]); // Debug logging
    } else {
        analyseForm.value.sollWert = '';
    }
};

const isWithinTolerance = (analyse) => {
    if (!analyse.istWert || !analyse.sollWert) return false;

    // Handle format "X - Y"
    if (analyse.sollWert.includes('-')) {
        const [min, max] = analyse.sollWert.split('-').map(val => parseFloat(val.trim().replace(',', '.')));
        const istWert = parseFloat(analyse.istWert.replace(',', '.'));
        return !isNaN(istWert) && !isNaN(min) && !isNaN(max) && istWert >= min && istWert <= max;
    }

    // Handle format "≤ X"
    if (analyse.sollWert.includes('≤')) {
        const max = parseFloat(analyse.sollWert.replace('≤', '').trim().replace(',', '.'));
        const istWert = parseFloat(analyse.istWert.replace(',', '.'));
        return !isNaN(istWert) && !isNaN(max) && istWert <= max;
    }

    return false;
};

const calculateToleranceText = (analyse) => {
    if (!analyse.istWert || !analyse.sollWert) return 'Keine Werte';
    return isWithinTolerance(analyse) ? 'Innerhalb der Toleranz' : 'Außerhalb der Toleranz';
};

const getToleranceRange = (sollWert) => {
    if (!sollWert) return '';

    // Handle format "X - Y"
    if (sollWert.includes('-')) {
        return sollWert;
    }

    // Handle format "≤ X"
    if (sollWert.includes('≤')) {
        return `0 - ${sollWert.replace('≤', '').trim()}`;
    }

    return '';
};

const getToleranceDeviation = (analyse) => {
    if (!analyse.istWert || !analyse.sollWert) return '';

    const istWert = parseFloat(analyse.istWert.replace(',', '.'));

    // Handle format "X - Y"
    if (analyse.sollWert.includes('-')) {
        const [min, max] = analyse.sollWert.split('-').map(val => parseFloat(val.trim().replace(',', '.')));
        if (istWert < min) {
            const absoluteDeviation = (istWert - min).toFixed(3);
            const percentDeviation = ((istWert - min) / min * 100).toFixed(2);
            return `${absoluteDeviation.replace('.', ',')} (${percentDeviation.replace('.', ',')}%)`;
        }
        if (istWert > max) {
            const absoluteDeviation = (istWert - max).toFixed(3);
            const percentDeviation = ((istWert - max) / max * 100).toFixed(2);
            return `+${absoluteDeviation.replace('.', ',')} (+${percentDeviation.replace('.', ',')}%)`;
        }
    }

    // Handle format "≤ X"
    if (analyse.sollWert.includes('≤')) {
        const max = parseFloat(analyse.sollWert.replace('≤', '').trim().replace(',', '.'));
        if (istWert > max) {
            const absoluteDeviation = (istWert - max).toFixed(3);
            const percentDeviation = ((istWert - max) / max * 100).toFixed(2);
            return `+${absoluteDeviation.replace('.', ',')} (+${percentDeviation.replace('.', ',')}%)`;
        }
    }

    return '';
};

const updateAvailableElements = () => {
    if (sollwerte.value) {
        // Define the desired order of elements
        const elementOrder = [
            'C', 'P', 'S', 'Cr', 'Cu', 'Mg', 'Mn', 'Mo', 'Ni', 'Si', 'Sn', 'Liq', 'UK_r',
            'Abstichtemperatur', 'Überhitzungstemperatur', 'Warmhaltetemperatur'
        ];

        // Create the elements array with proper formatting
        availableElements.value = elementOrder
            .map(elementKey => {
                let value = null;

                // Handle special cases for temperature values
                if (['Abstichtemperatur', 'Überhitzungstemperatur', 'Warmhaltetemperatur'].includes(elementKey)) {
                    const key = `${elementKey}_percent`;
                    value = sollwerte.value[key];
                } else {
                    // Handle different key formats for other elements
                    const possibleKeys = [
                        `${elementKey}_percent`,
                        `${elementKey.toLowerCase()}_percent`,
                        `${elementKey}_r_percent`,
                        `${elementKey.toLowerCase()}_r_percent`,
                        `Liq_r_percent`,
                        `Liq_percent`,
                        `UK_r_percent`
                    ];

                    for (const key of possibleKeys) {
                        if (sollwerte.value[key] !== undefined && sollwerte.value[key] !== null && sollwerte.value[key] !== '') {
                            value = sollwerte.value[key];
                            break;
                        }
                    }
                }

                if (value !== undefined && value !== "null") {
                    return {
                        key: elementKey,
                        value: value
                    };
                }
                return null;
            })
            .filter(element => element !== null);
    }
};

watch(() => sollwerte.value, (newValue) => {
    if (newValue) {
        updateAvailableElements();
    }
});

const selectElement = (element) => {
    analyseForm.value.element = element.key;
    showElementModal.value = false;
};

const updateAvailableEisenmarken = async () => {
    try {
        const response = await axios.get(route('spektrometer.sollwerte'), {
            params: {
                abteilung: form.spektrometer_daten.abteilung
            }
        });

        console.log('Rohdaten von API:', response.data);
        console.log('Ausgewählte Abteilung:', form.spektrometer_daten.abteilung);

        if (form.spektrometer_daten.abteilung === 'HF') {
            console.log('HF Abteilung ausgewählt');
            availableEisenmarken.value = response.data
                .filter(item => {
                    const parsedData = typeof item.data === 'string' ? JSON.parse(item.data) : item.data;
                    console.log('Verarbeite Item:', item);
                    console.log('Geparste Daten:', parsedData);
                    // Filter out all two-digit Eisenmarken
                    const number = parseInt(parsedData.EM_HF?.match(/\d+/)?.[0] || '0');
                    return parsedData && parsedData.EM_HF && (number < 10 || number >= 100);
                })
                .map(item => {
                    const parsedData = typeof item.data === 'string' ? JSON.parse(item.data) : item.data;
                    return {
                        value: parsedData.EM_HF,
                        details: parsedData
                    };
                })
                .sort((a, b) => parseInt(a.value) - parseInt(b.value));
        } else {
            availableEisenmarken.value = response.data
                .filter(item => {
                    const parsedData = typeof item.data === 'string' ? JSON.parse(item.data) : item.data;
                    // Filter out all two-digit Eisenmarken
                    const number = parseInt(parsedData.EM?.match(/\d+/)?.[0] || '0');
                    return parsedData && parsedData.EM && !parsedData.EM_HF && (number < 10 || number >= 100);
                })
                .map(item => {
                    const parsedData = typeof item.data === 'string' ? JSON.parse(item.data) : item.data;
                    return {
                        value: parsedData.EM,
                        details: parsedData
                    };
                })
                .sort((a, b) => parseInt(a.value) - parseInt(b.value));
        }

        console.log('Gefilterte Eisenmarken:', availableEisenmarken.value);
    } catch (error) {
        console.error('Fehler beim Laden der Eisenmarken:', error);
        availableEisenmarken.value = [];
    }
};

watch(() => form.spektrometer_daten.abteilung, (newValue) => {
    if (newValue) {
        updateAvailableEisenmarken();
    }
});

const selectEisenmarke = (eisenmarke) => {
    form.spektrometer_daten.eisenmarke = eisenmarke.value.toString();
    showEisenmarkeModal.value = false;
};
</script>

<template>
    <AppLayout>
        <form @submit.prevent="submit">
            <div class="max-w-7xl mx-auto">
                <!-- Modern Header with Breadcrumbs -->
                <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl shadow-lg mb-8 p-8">
                    <div class="flex flex-col space-y-4">
                        <!-- Breadcrumbs -->
                        <nav class="flex" aria-label="Breadcrumb">
                            <ol class="flex items-center space-x-4">
                                <li>
                                    <div>
                                        <Link :href="route('dashboard')" class="text-indigo-200 hover:text-white transition-colors duration-200">
                                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                                            </svg>
                                        </Link>
                                    </div>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <svg class="h-5 w-5 text-indigo-300" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        <span class="ml-4 text-sm font-medium text-white">Spektrometer</span>
                                    </div>
                                </li>
                            </ol>
                        </nav>

                        <!-- Header Content -->
                        <div class="flex justify-between items-center">
                            <div>
                                <h1 class="text-3xl font-bold text-white">
                                    Spektrometer Analyse
                                </h1>
                                <p class="mt-2 text-indigo-200">
                                    Erfassen Sie neue Spektrometer-Daten und erstellen Sie automatisch Rote Karten
                                </p>
                            </div>
                            <div class="hidden lg:block">
                                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                                    <div class="flex items-center space-x-2">
                                        <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                        </svg>
                                        <span class="text-white font-medium">Spektrometer</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Left Column (2/3) -->
                    <div class="lg:col-span-2 space-y-8">
                        <!-- Basis Informationen Card -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                        </div>
                                    </div>
                                    <div>
                                        <h2 class="text-lg font-semibold text-gray-900">Basis Informationen</h2>
                                        <p class="text-sm text-gray-600">Grundlegende Daten für die Spektrometer-Analyse</p>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">
                                            <span class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                                </svg>
                                                <span>Name</span>
                                                <span class="text-red-500">*</span>
                                            </span>
                                        </label>
                                        <div class="relative">
                                            <input type="text" v-model="form.spektrometer_daten.name"
                                                @keydown="handleKeyDown($event, 'datum')"
                                                id="name"
                                                placeholder="Ihr Name"
                                                :class="[
                                                    'block w-full rounded-lg border-2 px-4 py-3 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2',
                                                    !form.spektrometer_daten.name
                                                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50'
                                                        : 'border-gray-200 focus:border-indigo-500 focus:ring-indigo-500 bg-white hover:border-gray-300'
                                                ]">
                                        </div>
                                        <div v-if="!form.spektrometer_daten.name" class="flex items-center space-x-2 text-red-600">
                                            <svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <p class="text-sm font-medium">Bitte geben Sie Ihren Namen ein</p>
                                        </div>
                                    </div>

                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">
                                            <span class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                                </svg>
                                                <span>Datum</span>
                                                <span class="text-red-500">*</span>
                                            </span>
                                        </label>
                                        <div class="relative">
                                            <input type="date" v-model="form.spektrometer_daten.datum"
                                                @keydown="handleKeyDown($event, 'uhrzeit')"
                                                id="datum"
                                                :class="[
                                                    'block w-full rounded-lg border-2 px-4 py-3 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2',
                                                    !form.spektrometer_daten.datum
                                                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50'
                                                        : 'border-gray-200 focus:border-indigo-500 focus:ring-indigo-500 bg-white hover:border-gray-300'
                                                ]">
                                        </div>
                                        <div v-if="!form.spektrometer_daten.datum" class="flex items-center space-x-2 text-red-600">
                                            <svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <p class="text-sm font-medium">Bitte wählen Sie ein Datum aus</p>
                                        </div>
                                    </div>

                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">
                                            <span class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                                <span>Uhrzeit</span>
                                                <span class="text-red-500">*</span>
                                            </span>
                                        </label>
                                        <div class="relative">
                                            <input type="time" v-model="form.spektrometer_daten.uhrzeit"
                                                @keydown="handleKeyDown($event, 'chargennummer')"
                                                id="uhrzeit"
                                                :class="[
                                                    'block w-full rounded-lg border-2 px-4 py-3 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2',
                                                    !form.spektrometer_daten.uhrzeit
                                                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50'
                                                        : 'border-gray-200 focus:border-indigo-500 focus:ring-indigo-500 bg-white hover:border-gray-300'
                                                ]">
                                        </div>
                                        <div v-if="!form.spektrometer_daten.uhrzeit" class="flex items-center space-x-2 text-red-600">
                                            <svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <p class="text-sm font-medium">Bitte wählen Sie eine Uhrzeit aus</p>
                                        </div>
                                    </div>

                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">
                                            <span class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-1.414.586H7a4 4 0 01-4-4V7a4 4 0 014-4z"/>
                                                </svg>
                                                <span>Chargennummer</span>
                                                <span class="text-red-500">*</span>
                                            </span>
                                        </label>
                                        <div class="relative">
                                            <input type="text" v-model="form.spektrometer_daten.chargennummer"
                                                @keydown="handleKeyDown($event, 'abteilung')"
                                                id="chargennummer"
                                                placeholder="z.B. 123456"
                                                :class="[
                                                    'block w-full rounded-lg border-2 px-4 py-3 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2',
                                                    !form.spektrometer_daten.chargennummer
                                                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50'
                                                        : 'border-gray-200 focus:border-indigo-500 focus:ring-indigo-500 bg-white hover:border-gray-300'
                                                ]">
                                        </div>
                                        <div v-if="!form.spektrometer_daten.chargennummer" class="flex items-center space-x-2 text-red-600">
                                            <svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <p class="text-sm font-medium">Bitte geben Sie die Chargennummer ein</p>
                                        </div>
                                    </div>

                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">
                                            <span class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                                </svg>
                                                <span>Abteilung</span>
                                                <span class="text-red-500">*</span>
                                            </span>
                                        </label>
                                        <div class="relative">
                                            <select v-model="form.spektrometer_daten.abteilung"
                                                @keydown="handleKeyDown($event, 'eisenmarke')"
                                                id="abteilung"
                                                :class="[
                                                    'block w-full rounded-lg border-2 px-4 py-3 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 appearance-none bg-no-repeat bg-right',
                                                    !form.spektrometer_daten.abteilung
                                                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50'
                                                        : 'border-gray-200 focus:border-indigo-500 focus:ring-indigo-500 bg-white hover:border-gray-300'
                                                ]"
                                                style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 4 5\'><path fill=\'%23666\' d=\'M2 0L0 2h4zm0 5L0 3h4z\'/></svg>'); background-position: right 12px center; background-size: 12px;">
                                                <option value="">Bitte wählen</option>
                                                <option value="NG">NG - Kleinguss</option>
                                                <option value="GG">GG - Großguss</option>
                                                <option value="HF">HF - Handformerei</option>
                                            </select>
                                        </div>
                                        <div v-if="!form.spektrometer_daten.abteilung" class="flex items-center space-x-2 text-red-600">
                                            <svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <p class="text-sm font-medium">Bitte wählen Sie eine Abteilung aus</p>
                                        </div>
                                    </div>

                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">
                                            <span class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                                                </svg>
                                                <span>Eisenmarke</span>
                                                <span class="text-red-500">*</span>
                                            </span>
                                        </label>
                                        <div class="relative">
                                            <input type="text" v-model="form.spektrometer_daten.eisenmarke"
                                                @keydown="handleKeyDown($event, 'element')"
                                                id="eisenmarke"
                                                placeholder="Klicken Sie auf das Symbol zum Auswählen"
                                                readonly
                                                :class="[
                                                    'block w-full rounded-lg border-2 px-4 py-3 pr-12 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 cursor-pointer',
                                                    !form.spektrometer_daten.eisenmarke
                                                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50'
                                                        : 'border-gray-200 focus:border-indigo-500 focus:ring-indigo-500 bg-white hover:border-gray-300'
                                                ]">
                                            <button
                                                v-if="form.spektrometer_daten.abteilung"
                                                @click="showEisenmarkeModal = true"
                                                type="button"
                                                class="absolute right-3 top-1/2 -translate-y-1/2 inline-flex items-center justify-center rounded-lg w-8 h-8 bg-indigo-100 text-indigo-600 hover:bg-indigo-200 hover:text-indigo-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                                title="Verfügbare Eisenmarken anzeigen">
                                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"/>
                                                </svg>
                                            </button>
                                        </div>
                                        <div v-if="sollwerteError" class="flex items-center space-x-2 text-red-600">
                                            <svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <p class="text-sm font-medium">{{ sollwerteError }}</p>
                                        </div>
                                        <div v-else-if="!form.spektrometer_daten.eisenmarke" class="flex items-center space-x-2 text-red-600">
                                            <svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <p class="text-sm font-medium">Bitte wählen Sie eine Eisenmarke aus</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Analysewerte Eingabe Card -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                            </svg>
                                        </div>
                                    </div>
                                    <div>
                                        <h2 class="text-lg font-semibold text-gray-900">Analysewerte Eingabe</h2>
                                        <p class="text-sm text-gray-600">Fügen Sie Elemente und deren Werte hinzu</p>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <!-- Analyse Form -->
                                <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6">
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">
                                            <span class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3H5a2 2 0 00-2 2v12a4 4 0 004 4h2a2 2 0 002-2V5a2 2 0 00-2-2z"/>
                                                </svg>
                                                <span>Element</span>
                                            </span>
                                        </label>
                                        <div class="relative">
                                            <input type="text" v-model="analyseForm.element"
                                                @keydown="handleKeyDown($event, 'istWert')"
                                                @input="updateSollWert"
                                                id="element"
                                                placeholder="Element auswählen"
                                                readonly
                                                class="block w-full rounded-lg border-2 border-gray-200 px-4 py-3 pr-12 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:border-blue-500 focus:ring-blue-500 bg-white hover:border-gray-300 cursor-pointer">

                                            <button
                                                v-if="sollwerte"
                                                @click="showElementModal = true"
                                                type="button"
                                                class="absolute right-3 top-1/2 -translate-y-1/2 inline-flex items-center justify-center rounded-lg w-8 h-8 bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                                title="Verfügbare Elemente anzeigen">
                                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"/>
                                                </svg>
                                            </button>
                                        </div>
                                        <!-- Hinweis-Text bei Nicht analysierbar -->
                                        <div v-if="analyseForm.element === 'Nicht analysierbar'" class="flex items-center space-x-2 text-amber-600 bg-amber-50 px-3 py-2 rounded-lg">
                                            <svg class="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                            </svg>
                                            <span class="text-sm font-medium">Als nicht analysierbar markiert</span>
                                        </div>
                                    </div>
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">
                                            <span class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                                </svg>
                                                <span>Ist-Wert</span>
                                            </span>
                                        </label>
                                        <input type="text" v-model="analyseForm.istWert"
                                            @keydown="handleKeyDown($event, 'sollWert')"
                                            id="istWert"
                                            placeholder="Messwert eingeben"
                                            class="block w-full rounded-lg border-2 border-gray-200 px-4 py-3 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:border-blue-500 focus:ring-blue-500 bg-white hover:border-gray-300">
                                    </div>
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">
                                            <span class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                                                </svg>
                                                <span>Soll-Wert</span>
                                            </span>
                                        </label>
                                        <input type="text" v-model="analyseForm.sollWert"
                                            @keydown="handleKeyDown($event, 'addAnalyseButton')"
                                            id="sollWert"
                                            placeholder="Automatisch geladen"
                                            :readonly="!!sollwerte"
                                            :class="[
                                                'block w-full rounded-lg border-2 px-4 py-3 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2',
                                                !!sollwerte
                                                    ? 'border-gray-200 bg-gray-50 text-gray-700 cursor-not-allowed'
                                                    : 'border-gray-200 focus:border-blue-500 focus:ring-blue-500 bg-white hover:border-gray-300'
                                            ]">
                                    </div>
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">
                                            <span class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                                </svg>
                                                <span>Toleranz</span>
                                            </span>
                                        </label>
                                        <div class="h-[52px] flex items-center">
                                            <div v-if="analyseForm.istWert && analyseForm.sollWert"
                                                :class="[
                                                    'px-4 py-2 rounded-lg flex items-center gap-2 w-full',
                                                    isWithinTolerance(analyseForm)
                                                        ? 'bg-green-100 text-green-800 border-2 border-green-200'
                                                        : 'bg-red-100 text-red-800 border-2 border-red-200'
                                                ]">
                                                <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path v-if="isWithinTolerance(analyseForm)" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                                <div class="flex flex-col">
                                                    <span class="font-semibold text-sm">
                                                        {{ isWithinTolerance(analyseForm) ? 'Innerhalb' : 'Außerhalb' }}
                                                    </span>
                                                    <span class="text-xs opacity-75">
                                                        {{ getToleranceRange(analyseForm.sollWert) }}
                                                    </span>
                                                </div>
                                            </div>
                                            <div v-else class="text-gray-400 text-sm italic">
                                                Werte eingeben für Toleranzprüfung
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="border-t border-gray-100 pt-6">
                                    <div v-if="!analyseForm.element || (!analyseForm.istWert || !analyseForm.sollWert) && analyseForm.element !== 'Nicht analysierbar'" class="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                                        <div class="flex items-center space-x-2 text-amber-700">
                                            <svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <span class="text-sm font-medium">Bitte füllen Sie alle Felder aus, um eine Analyse hinzuzufügen</span>
                                        </div>
                                    </div>

                                    <div class="flex flex-wrap gap-3">
                                        <button type="button"
                                            id="addAnalyseButton"
                                            @click="addAnalyse"
                                            :disabled="!analyseForm.element || ((!analyseForm.istWert || !analyseForm.sollWert) && analyseForm.element !== 'Nicht analysierbar')"
                                            class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200">
                                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                            </svg>
                                            Analyse hinzufügen
                                        </button>

                                        <button type="button"
                                            @click="() => {
                                                    if (analyseForm.element === 'Nicht analysierbar') {
                                                        // Zurücksetzen, wenn bereits 'Nicht analysierbar' ist
                                                        analyseForm.element = '';
                                                        analyseForm.istWert = '';
                                                        analyseForm.sollWert = '';
                                                        if (sollwerte) updateSollWert();
                                                    } else {
                                                        // Auf 'Nicht analysierbar' setzen
                                                        analyseForm.element = 'Nicht analysierbar';
                                                        analyseForm.istWert = '-';
                                                        analyseForm.sollWert = '-';
                                                    }
                                                }"
                                            :class="[
                                                    'inline-flex items-center px-6 py-3 border rounded-lg shadow-sm text-sm font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2',
                                                    analyseForm.element === 'Nicht analysierbar'
                                                        ? 'border-transparent text-white bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 focus:ring-red-500'
                                                        : 'border-red-300 text-red-700 bg-white hover:bg-red-50 focus:ring-red-500'
                                                ]"
                                            :title="analyseForm.element === 'Nicht analysierbar' ? 'Nicht analysierbar zurücksetzen' : 'Nicht analysierbar markieren'">
                                            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path v-if="analyseForm.element === 'Nicht analysierbar'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                            {{ analyseForm.element === 'Nicht analysierbar' ? 'Zurücksetzen' : 'Nicht analysierbar' }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Analysewerte Übersicht Card -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div class="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                                </svg>
                                            </div>
                                        </div>
                                        <div>
                                            <h2 class="text-lg font-semibold text-gray-900">Analysewerte Übersicht</h2>
                                            <p class="text-sm text-gray-600">{{ form.spektrometer_daten.analysewerte.length }} Werte erfasst</p>
                                        </div>
                                    </div>
                                    <div v-if="form.spektrometer_daten.analysewerte.length > 0" class="flex items-center space-x-2">
                                        <div class="text-right">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ form.spektrometer_daten.analysewerte.filter(a => isWithinTolerance(a)).length }} / {{ form.spektrometer_daten.analysewerte.length }}
                                            </div>
                                            <div class="text-xs text-gray-500">innerhalb Toleranz</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Analysewerte Table -->
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                Element
                                            </th>
                                            <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                Ist-Wert
                                            </th>
                                            <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                Soll-Wert
                                            </th>
                                            <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                Toleranz
                                            </th>
                                            <th scope="col" class="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                Aktionen
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr v-if="form.spektrometer_daten.analysewerte.length === 0">
                                            <td colspan="5" class="px-6 py-12 text-center">
                                                <div class="flex flex-col items-center space-y-3">
                                                    <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                                                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                                        </svg>
                                                    </div>
                                                    <div class="text-sm text-gray-500">
                                                        Noch keine Analysewerte vorhanden
                                                    </div>
                                                    <div class="text-xs text-gray-400">
                                                        Fügen Sie oben Elemente und deren Werte hinzu
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr v-for="(analyse, index) in form.spektrometer_daten.analysewerte" :key="index"
                                            class="hover:bg-gray-50 transition-colors duration-150">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center space-x-2">
                                                    <div class="w-2 h-2 rounded-full" :class="isWithinTolerance(analyse) ? 'bg-green-400' : 'bg-red-400'"></div>
                                                    <span class="text-sm font-medium text-gray-900">{{ analyse.element }}</span>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 font-mono">{{ analyse.istWert }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 font-mono">{{ analyse.sollWert }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span :class="[
                                                    'inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold',
                                                    isWithinTolerance(analyse)
                                                        ? 'bg-green-100 text-green-800 border border-green-200'
                                                        : 'bg-red-100 text-red-800 border border-red-200'
                                                ]">
                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path v-if="isWithinTolerance(analyse)" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                                        <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                    </svg>
                                                    <template v-if="!isWithinTolerance(analyse)">
                                                        {{ getToleranceDeviation(analyse) }}
                                                    </template>
                                                    <template v-else>
                                                        OK
                                                    </template>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                                <button @click="form.spektrometer_daten.analysewerte.splice(index, 1)"
                                                    class="inline-flex items-center justify-center w-8 h-8 rounded-lg text-gray-400 hover:text-red-600 hover:bg-red-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                                    title="Analysewert entfernen">
                                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column (1/3) -->
                    <div class="space-y-8">
                        <!-- Fehlercode Card -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div class="bg-gradient-to-r from-orange-50 to-red-50 px-6 py-4 border-b border-gray-200">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                            </svg>
                                        </div>
                                    </div>
                                    <div>
                                        <h2 class="text-lg font-semibold text-gray-900">Fehlercode</h2>
                                        <p class="text-sm text-gray-600">Automatische Zuordnung oder manuelle Auswahl</p>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="space-y-4">
                                    <!-- Fehlercode Input -->
                                    <div class="relative">
                                        <input
                                            type="text"
                                            v-model="form.spektrometer_daten.fehlercode"
                                            class="block w-full rounded-lg border-2 border-gray-200 px-4 py-3 pr-4 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:border-orange-500 focus:ring-orange-500 bg-gray-50 cursor-not-allowed"
                                            placeholder="Fehlercode wird automatisch zugeordnet"
                                            readonly
                                        />
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="grid grid-cols-1 gap-3">
                                        <button
                                            type="button"
                                            @click="autoSuggestFehlercode"
                                            class="inline-flex items-center justify-center px-4 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
                                            title="Fehlercode automatisch zuordnen">
                                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                            </svg>
                                            Automatisch zuordnen
                                        </button>

                                        <button
                                            type="button"
                                            @click="showFehlercodeModal = true"
                                            class="inline-flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-semibold text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200"
                                            title="Verfügbare Fehlercodes anzeigen">
                                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                            </svg>
                                            Manuell auswählen
                                        </button>

                                        <button
                                            v-if="form.spektrometer_daten.fehlercode"
                                            type="button"
                                            @click="form.spektrometer_daten.fehlercode = ''; selectedFehlercode = null"
                                            class="inline-flex items-center justify-center px-4 py-3 border border-red-300 rounded-lg shadow-sm text-sm font-semibold text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200"
                                            title="Fehlercode löschen">
                                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                            Löschen
                                        </button>
                                    </div>
                                </div>

                                <!-- Fehlercode Information -->
                                <div v-if="selectedFehlercode" class="mt-6 space-y-3">
                                    <div v-if="selectedFehlercode.teilesperren" class="p-4 bg-red-50 border border-red-200 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="flex-shrink-0">
                                                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <h4 class="text-sm font-semibold text-red-800">Teilesperrung</h4>
                                                <p class="text-sm text-red-700">Dieser Fehlercode führt zu einer Teilesperrung!</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div v-if="selectedFehlercode.massnahmen" class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                        <div class="flex items-start space-x-3">
                                            <div class="flex-shrink-0">
                                                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <h4 class="text-sm font-semibold text-blue-800">Maßnahmen</h4>
                                                <p class="text-sm text-blue-700">{{ selectedFehlercode.massnahmen }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Proben Card -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div class="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
                                                </svg>
                                            </div>
                                        </div>
                                        <div>
                                            <h2 class="text-lg font-semibold text-gray-900">Proben</h2>
                                            <p class="text-sm text-gray-600">{{ form.spektrometer_daten.proben.length }} Proben erfasst</p>
                                        </div>
                                    </div>
                                    <button type="button"
                                        @click="addProbe"
                                        :disabled="!probenForm.probenummer || !probenForm.gidNummer"
                                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200">
                                        <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                        Hinzufügen
                                    </button>
                                </div>
                            </div>
                            <div class="p-6">

                                <div class="space-y-4">
                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">
                                            <span class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-1.414.586H7a4 4 0 01-4-4V7a4 4 0 014-4z"/>
                                                </svg>
                                                <span>Probennummer</span>
                                            </span>
                                        </label>
                                        <input type="text" v-model="probenForm.probenummer"
                                            @keydown="handleKeyDown($event, 'gidNummer')"
                                            id="probenummer"
                                            placeholder="z.B. 123456"
                                            class="block w-full rounded-lg border-2 border-gray-200 px-4 py-3 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:border-purple-500 focus:ring-purple-500 bg-white hover:border-gray-300">
                                        <div v-if="!probenForm.probenummer" class="flex items-center space-x-2 text-purple-600">
                                            <svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <p class="text-sm font-medium">Bitte geben Sie eine Probennummer ein</p>
                                        </div>
                                    </div>

                                    <div class="space-y-2">
                                        <label class="block text-sm font-semibold text-gray-700">
                                            <span class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                </svg>
                                                <span>GID Nummer</span>
                                            </span>
                                        </label>
                                        <input type="text" v-model="probenForm.gidNummer"
                                            @keydown="handleKeyDown($event, 'addProbeButton')"
                                            id="gidNummer"
                                            placeholder="z.B. 123456"
                                            class="block w-full rounded-lg border-2 border-gray-200 px-4 py-3 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:border-purple-500 focus:ring-purple-500 bg-white hover:border-gray-300">
                                        <div v-if="!probenForm.gidNummer" class="flex items-center space-x-2 text-purple-600">
                                            <svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <p class="text-sm font-medium">Bitte geben Sie eine GID Nummer ein</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Proben Table -->
                                <div class="mt-6 overflow-hidden rounded-lg border border-gray-200">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Probe-Nr.</th>
                                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">GID-Nr.</th>
                                                <th class="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Aktionen</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr v-if="form.spektrometer_daten.proben.length === 0">
                                                <td colspan="3" class="px-6 py-8 text-center">
                                                    <div class="flex flex-col items-center space-y-2">
                                                        <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
                                                            </svg>
                                                        </div>
                                                        <span class="text-sm text-gray-500">Noch keine Proben vorhanden</span>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr v-for="(probe, index) in form.spektrometer_daten.proben" :key="index"
                                                class="hover:bg-gray-50 transition-colors duration-150">
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 font-mono">
                                                    {{ probe.probenummer }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 font-mono">
                                                    {{ probe.gidNummer }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                                    <button @click="form.spektrometer_daten.proben.splice(index, 1)"
                                                        class="inline-flex items-center justify-center w-8 h-8 rounded-lg text-gray-400 hover:text-red-600 hover:bg-red-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                                        title="Probe entfernen">
                                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                        </svg>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- System Information -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div class="bg-gradient-to-r from-gray-50 to-slate-50 px-6 py-4 border-b border-gray-200">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                                            </svg>
                                        </div>
                                    </div>
                                    <div>
                                        <h2 class="text-lg font-semibold text-gray-900">System Information</h2>
                                        <p class="text-sm text-gray-600">Automatisch erfasste Daten</p>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <span class="text-sm font-medium text-gray-700">Benutzer</span>
                                        <span class="text-sm text-gray-900 font-mono">{{ form.spektrometer_daten.system_info.current.username }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bemerkungen -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div class="bg-gradient-to-r from-yellow-50 to-amber-50 px-6 py-4 border-b border-gray-200">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                            </svg>
                                        </div>
                                    </div>
                                    <div>
                                        <h2 class="text-lg font-semibold text-gray-900">Bemerkungen</h2>
                                        <p class="text-sm text-gray-600">Zusätzliche Anmerkungen und Hinweise</p>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <textarea v-model="form.spektrometer_daten.bemerkungen"
                                    rows="4"
                                    placeholder="Bitte tragen Sie hier alle relevanten Anmerkungen zu den Teilen mit Rotekarte im laufenden Prozess ein..."
                                    class="block w-full rounded-lg border-2 border-gray-200 px-4 py-3 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:border-yellow-500 focus:ring-yellow-500 bg-white hover:border-gray-300 resize-none"></textarea>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div class="p-6">
                                <div class="space-y-4">
                                    <button type="submit"
                                        :disabled="form.processing"
                                        class="w-full inline-flex justify-center items-center py-4 px-6 border border-transparent rounded-lg shadow-sm text-base font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200">
                                        <svg v-if="form.processing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        <svg v-else class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        {{ form.processing ? 'Wird gespeichert...' : 'Spektrometer-Daten speichern' }}
                                    </button>
                                    <Link :href="route('dashboard')"
                                        class="w-full inline-flex justify-center items-center py-3 px-6 border border-gray-300 rounded-lg shadow-sm text-sm font-semibold text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
                                        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                                        </svg>
                                        Zurück zum Dashboard
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <!-- Element Modal -->
        <div v-if="showElementModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
                <!-- Modal Header -->
                <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-xl font-semibold text-white">Verfügbare Elemente</h3>
                            <p class="text-blue-100 text-sm">für Eisenmarke {{ form.spektrometer_daten.eisenmarke }}</p>
                        </div>
                        <button @click="showElementModal = false"
                            class="text-blue-100 hover:text-white transition-colors duration-200 p-2 rounded-lg hover:bg-white/10">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Modal Content -->
                <div class="p-6 max-h-96 overflow-y-auto">
                    <div class="grid grid-cols-1 gap-3">
                        <button
                            v-for="element in availableElements"
                            :key="element.key"
                            @click="selectElement(element)"
                            class="text-left p-4 hover:bg-blue-50 rounded-xl border border-gray-200 hover:border-blue-300 transition-all duration-200 group">
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="font-semibold text-gray-900 group-hover:text-blue-700">{{ element.key }}</span>
                                    <span v-if="!['Abstichtemperatur', 'Überhitzungstemperatur', 'Warmhaltetemperatur'].includes(element.key)"
                                          class="text-gray-600 text-sm ml-2">({{ getElementName(element.key) }})</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-gray-500 font-mono text-sm">{{ element.value }}</span>
                                    <svg class="h-4 w-4 text-gray-400 group-hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                    </svg>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Eisenmarke Modal -->
        <div v-if="showEisenmarkeModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-lg w-full max-h-[80vh] overflow-hidden">
                <!-- Modal Header -->
                <div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-xl font-semibold text-white">Verfügbare Eisenmarken</h3>
                            <p class="text-indigo-100 text-sm">{{ form.spektrometer_daten.abteilung }}-Abteilung</p>
                        </div>
                        <button @click="showEisenmarkeModal = false"
                            class="text-indigo-100 hover:text-white transition-colors duration-200 p-2 rounded-lg hover:bg-white/10">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Modal Content -->
                <div class="p-6 max-h-96 overflow-y-auto">
                    <div class="grid grid-cols-2 gap-3">
                        <button
                            v-for="eisenmarke in availableEisenmarken"
                            :key="eisenmarke.value"
                            @click="selectEisenmarke(eisenmarke)"
                            class="text-center p-4 hover:bg-indigo-50 rounded-xl border border-gray-200 hover:border-indigo-300 transition-all duration-200 group">
                            <div class="flex flex-col items-center space-y-2">
                                <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center group-hover:bg-indigo-200">
                                    <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                                    </svg>
                                </div>
                                <span class="font-semibold text-gray-900 group-hover:text-indigo-700">EM {{ eisenmarke.value }}</span>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Fehlercode Modal -->
        <TransitionRoot appear :show="showFehlercodeModal" as="template">
            <Dialog as="div" @close="showFehlercodeModal = false" class="relative z-10">
                <TransitionChild
                    as="template"
                    enter="duration-300 ease-out"
                    enter-from="opacity-0"
                    enter-to="opacity-100"
                    leave="duration-200 ease-in"
                    leave-from="opacity-100"
                    leave-to="opacity-0"
                >
                    <div class="fixed inset-0 bg-black bg-opacity-25" />
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild
                            as="template"
                            enter="duration-300 ease-out"
                            enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100"
                            leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95"
                        >
                            <DialogPanel class="w-full max-w-6xl transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all">
                                <!-- Modal Header -->
                                <div class="bg-gradient-to-r from-orange-600 to-red-600 px-6 py-4">
                                    <DialogTitle as="h3" class="text-xl font-semibold text-white">
                                        Fehlercode auswählen
                                    </DialogTitle>
                                    <p class="text-orange-100 text-sm mt-1">Wählen Sie den passenden Fehlercode aus der Liste</p>
                                </div>

                                <!-- Modal Content -->
                                <div class="p-6">
                                    <div class="overflow-hidden rounded-xl border border-gray-200 shadow-sm">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
                                                <tr>
                                                    <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Fehlercode</th>
                                                    <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Eisenmarke</th>
                                                    <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Element</th>
                                                    <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Absolutwerte</th>
                                                    <th class="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Auswählen</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                <tr v-for="code in availableFehlercodes" :key="code.id"
                                                    class="hover:bg-orange-50 transition-colors duration-150">
                                                    <td class="px-6 py-4 text-sm font-semibold text-gray-900">{{ code.fehlercode }}</td>
                                                    <td class="px-6 py-4 text-sm text-gray-700">{{ code.eisenmarke }}</td>
                                                    <td class="px-6 py-4 text-sm text-gray-700">{{ code.element }}</td>
                                                    <td class="px-6 py-4 text-sm text-gray-700 font-mono">{{ code.absolutwerte }}</td>
                                                    <td class="px-6 py-4 text-center">
                                                        <button
                                                            @click="selectFehlercode(code)"
                                                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-semibold rounded-lg text-white bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200"
                                                        >
                                                            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                                            </svg>
                                                            Auswählen
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Modal Footer -->
                                <div class="bg-gray-50 px-6 py-4 flex justify-end">
                                    <button
                                        type="button"
                                        class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-semibold text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
                                        @click="showFehlercodeModal = false"
                                    >
                                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                        Schließen
                                    </button>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </AppLayout>
</template>
