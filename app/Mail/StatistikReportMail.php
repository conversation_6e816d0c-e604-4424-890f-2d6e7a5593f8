<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Attachment;

class StatistikReportMail extends Mailable
{
    use Queueable, SerializesModels;

    public $statistiken;
    public $filter;
    public $pdfPath;

    /**
     * Create a new message instance.
     */
    public function __construct($statistiken, $filter, $pdfPath = null)
    {
        $this->statistiken = $statistiken;
        $this->filter = $filter;
        $this->pdfPath = $pdfPath;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $zeitraum = $this->filter['start_date'] . ' bis ' . $this->filter['end_date'];
        
        return new Envelope(
            subject: 'Rotekarten Statistik-Report - ' . $zeitraum,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        // Verwende das optimierte Template für bessere E-Mail-Client-Kompatibilität
        return new Content(
            view: 'emails.statistik-report',
            with: [
                'statistiken' => $this->statistiken,
                'filter' => $this->filter,
                'zeitraum' => $this->filter['start_date'] . ' bis ' . $this->filter['end_date'],
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $attachments = [];
        
        if ($this->pdfPath && file_exists($this->pdfPath)) {
            $zeitraum = str_replace(['-', ' '], ['', '_'], $this->filter['start_date'] . '_bis_' . $this->filter['end_date']);
            $filename = 'Statistik_Report_' . $zeitraum . '.pdf';
            
            $attachments[] = Attachment::fromPath($this->pdfPath)
                ->as($filename)
                ->withMime('application/pdf');
        }
        
        return $attachments;
    }
}
